{"app": {"name": "Fulbito Stats", "tagline": "Track player performance and generate balanced teams"}, "landing": {"title": "Welcome to Fulbito Stats", "subtitle": "The ultimate tool for soccer team management", "description": "Fulbito Stats helps you track player performance, generate balanced teams, and analyze match statistics for your amateur soccer teams.", "features": "Key Features", "feature1Title": "Player Performance Tracking", "feature1Description": "Track individual player performance with detailed statistics, ratings, and comprehensive analytics.", "feature2Title": "Smart Team Generator", "feature2Description": "Create balanced teams based on player ratings, performance history, and chemistry insights.", "feature3Title": "Match Recording & Analysis", "feature3Description": "Record match results and analyze performance trends over time with detailed statistics.", "feature4Title": "Chemistry Insights", "feature4Description": "Discover which players perform best together with our unique chemistry tracking system.", "feature5Title": "Leaderboard Rankings", "feature5Description": "Track player rankings with filtering options and comprehensive leaderboard statistics.", "feature6Title": "World Cup Run Tracking", "feature6Description": "Monitor player performance in tournament-style runs with group stage and knockout tracking.", "getStarted": "Get Started", "login": "<PERSON><PERSON>", "signup": "Sign Up", "watchDemo": "Watch Demo", "liveDemo": "Live Demo"}, "nav": {"dashboard": "Dashboard", "players": "Players", "matches": "Matches", "leaderboard": "Leaderboard", "teamGenerator": "Team Generator", "chemistry": "Chemistry", "profile": "Profile"}, "auth": {"login": "<PERSON><PERSON>", "signup": "Sign Up", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "loginWithGoogle": "Login with Google", "loginWithGithub": "Login with <PERSON><PERSON><PERSON>", "noAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "createAccount": "Create Account", "resetPassword": "Reset Password", "resetPasswordInstructions": "Enter your email and we'll send you a link to reset your password.", "signOut": "Sign Out"}, "dashboard": {"overview": "Overview", "welcome": "Welcome to your Fulbito Stats Dashboard", "getStarted": "Get started by adding players and recording matches to see statistics and insights.", "addFirstPlayer": "Add Your First Player", "totalPlayers": "Total Players", "activePlayers": "Active players in your group", "noPlayers": "No players yet", "totalMatches": "Total Matches", "recentMatches": "Recent matches", "noMatches": "No matches yet", "highestRating": "Highest Rating", "highestWinRate": "Highest Win Rate", "playerStats": "Player Stats", "searchPlayers": "Search players...", "noPlayersAdded": "No Players Added Yet", "addPlayersDescription": "Add players to view their statistics and performance metrics.", "addPlayers": "Add Players", "currentLeader": "Current Leader", "addPlayersToSeeLeader": "Add players to see the leader", "recentMatchesTitle": "Recent Matches", "recentMatchesDescription": "The 3 most recent matches played in your group", "viewAllMatches": "View All Matches", "noRecentMatches": "No recent matches found", "recordMatch": "Record a Match", "bestChemistry": "Best Chemistry", "viewAllChemistry": "View All Chemistry", "groupStats": "Group Stats", "leader": "Leader", "playerSnapshot": "Player Snapshot"}, "players": {"title": "Players", "addPlayer": "Add Player", "editPlayer": "Edit Player", "deletePlayer": "Delete Player", "name": "Name", "rating": "Rating", "winRate": "Win Rate", "played": "Played", "won": "Won", "lost": "Lost", "drawn": "Drawn", "goals": "Goals", "assists": "Assists", "skills": "Skills", "effort": "<PERSON><PERSON><PERSON>", "stamina": "Stamina", "averageRating": "Average Rating", "worldCupRun": "World Cup Run", "worldCupRunTooltip": "Simulated World Cup tournament based on last 7 matches", "worldCupRunsWon": "World Cup Runs Won", "worldChampion": "World Champion", "qualified": "Knockouts (0/4 wins)", "qualifiedWithPoints": "Knockouts ({{wins}}/4 wins)", "notQualified": "Not Qualified", "eliminated": "Eliminated", "notStarted": "Not Started", "groupStage": "Group Stage", "groupStageOngoing": "Qualifying ({{points}}/9 pts)", "needsWinOrDraw": "Win or Draw to Qualify", "knockoutProgress": "Knockout", "points": "pts", "matches": "matches", "needsPoints": "Needs", "morePoints": "more pts", "searchPlayers": "Search players...", "noPlayersFound": "No players found", "confirmDelete": "Are you sure you want to delete this player?", "deleteWarning": "This action cannot be undone. All player statistics will be permanently removed.", "playerSelected": "Player Selected", "viewingStatsFor": "Viewing stats for {{name}}", "fetchError": "Failed to fetch players.", "nameRequired": "Player name cannot be empty.", "noPlayersDescription": "Add players to start tracking their performance and statistics.", "loadingPlayers": "Loading player data", "deleteConfirmMessage": "Are you sure you want to delete {{name}}? This action cannot be undone.", "deleting": "Deleting...", "updating": "Updating...", "adding": "Adding...", "updateSuccess": "Player updated successfully!", "addSuccess": "Player added successfully!", "deleteSuccess": "Player deleted successfully!", "playerRemoved": "{{name}} has been removed.", "deleteError": "Error deleting player", "updateError": "Error updating player", "addError": "Error adding player", "record": "Record"}, "matches": {"title": "Matches", "addMatch": "Add Match", "addFirstMatch": "Add First Match", "editMatch": "Edit Match", "deleteMatch": "Delete Match", "deleteSelectedMatches": "Delete Selected Matches", "deleteSelected": "Delete ({{count}}) Selected", "deleteMatches": "Delete {{count}} Matches", "deleteMatchConfirm": "Are you sure you want to delete Match #{{id}}? This action cannot be undone.", "deleteSelectedMatchesConfirm": "Are you sure you want to delete {{count}} selected match(es)? This action cannot be undone.", "date": "Date", "teamA": "Team A", "teamB": "Team B", "teamAScore": "Team A Score (Optional)", "teamBScore": "Team B Score (Optional)", "score": "Score", "result": "Result", "matchResult": "Match Result", "searchMatches": "Search matches...", "searchPlayers": "Search players (e.g., Player1, Player2)", "noMatchesFound": "No matches found", "noMatchesRecorded": "No Matches Recorded Yet", "noMatchesDescription": "Start tracking your matches to see player performance and team statistics.", "noMatchesMatchFilters": "No Matches Match Your Filters", "adjustFilters": "Try adjusting your filter criteria to see more results.", "clearFilters": "Clear Filters", "confirmDelete": "Are you sure you want to delete this match?", "deleteWarning": "This action cannot be undone. All match statistics will be permanently removed.", "youtubeLink": "YouTube Link", "youtubeLinkPlaceholder": "Optional (e.g., https://youtu.be/...)", "addYoutubeLink": "Add YouTube Link", "comments": {"title": "Comments", "description": "Discuss this match with your teammates", "loading": "Loading comments...", "noComments": "No comments yet", "beFirst": "Be the first to comment on this match", "matchComments": "Match comments", "deleteComment": "Delete comment", "addCommentForm": "Add comment form", "addCommentPlaceholder": "Add a comment... (Use @ to mention players)", "tip": "Tip: Type @ to mention players. Press Ctrl+Enter to submit.", "posting": "Posting...", "postComment": "Post Comment", "loginToComment": "Please log in to comment", "error": "Error", "loadError": "Failed to load comments", "enterComment": "Please enter a comment", "posted": "Comment posted", "addSuccess": "Your comment has been added successfully", "addErrorTryAgain": "Failed to add comment. Please try again.", "addError": "Failed to add comment", "confirmDelete": "Are you sure you want to delete this comment?", "deleted": "Comment deleted", "deleteSuccess": "Your comment has been removed", "deleteSuccessAnnounce": "Comment deleted successfully", "deleteError": "Failed to delete comment", "mentionedPlayer": "Mentioned player: {{name}}"}, "addComment": "Add Comment", "teamAWon": "Team A Won", "teamBWon": "Team B Won", "draw": "Draw", "win": "Win", "loss": "Loss", "goal": "goal", "goals": "goals", "game": "game", "games": "games", "goalscorers": "Goalscorers (Optional)", "selectPlayersFirst": "Select players first", "autoCalculate": "Auto-calculate from goalscorers", "leaveEmpty": "Leave empty for no score", "saveChanges": "Save Changes", "loadingMatches": "Loading matches...", "watchHighlights": "Watch Highlights", "fetchError": "Error fetching matches", "deleteError": "Error deleting match", "deleteSuccess": "Match deleted successfully!", "bulkDeleteError": "Error deleting selected matches", "bulkDeleteSuccess": "{{count}} match(es) deleted successfully!", "invalidForm": "Invalid form", "formRequirements": "Please enter a date and either scores, a winner, or goalscorers.", "updateError": "Error updating match", "addError": "Error adding match", "updateSuccess": "Match updated successfully!", "addSuccess": "Match added successfully!", "start": "Start", "end": "End", "dateRange": "Date Range", "clear": "Clear", "apply": "Apply", "allResults": "All results", "wins": "Wins", "losses": "Losses", "draws": "Draws", "sortBy": "Sort by", "newestFirst": "Newest first", "oldestFirst": "Oldest first", "highestScore": "Highest Score", "lowestScore": "Lowest Score", "resetFilters": "Reset filters", "searchPlaceholder": "Search matches by player name...", "noMatchesSearch": "No matches match your search", "team": "Team", "lastMatch": "Last Match", "showingMatches": "Showing {{count}} of {{total}} matches", "noMatches": "No matches found", "noMatchesDesc": "Start tracking your matches to see player performance and team statistics.", "noMatchesFiltered": "No matches match your current filters", "filters": "Filters", "filterByResult": "Filter by result", "winsOnly": "Wins only", "drawsOnly": "Draws only", "teams": "Teams", "format": "Format", "teamAWin": "Team A Win", "teamBWin": "Team B Win"}, "teamGenerator": {"title": "Team Generator", "availablePlayers": "Available Players", "teamA": "Team A", "teamB": "Team B", "teamAScore": "Team A Score", "teamBScore": "Team B Score", "generateTeams": "Generate Teams", "saveMatch": "Save Match", "saveGeneratedMatch": "Save Generated Match", "saveAsMatchDisabled": "Save as Match (Disabled in View Mode)", "balanceTeams": "Balance Teams", "clearTeams": "Clear Teams", "addPlayer": "Add Player", "automatic": "Automatic", "manual": "Manual", "searchPlayers": "Search players...", "searchPlayersWithCommas": "Search players (separate multiple names with commas)...", "noPlayersAvailable": "No players available", "teamImbalanceWarning": "Teams are not balanced in quantity", "autoGenerate": "Auto Generate", "manualEditor": "Manual Editor", "loadError": "Error loading players", "fetchError": "Failed to fetch player data", "insufficientPlayers": "Insufficient Players", "selectAtLeast": "Please select at least {{count}} players for the {{format}} format.", "tooManyPlayers": "Too Many Players", "selectExactly": "Please select exactly {{count}} players for the {{format}} format. You have {{selected}}.", "savedPreferenceRequires": "The saved preference requires {{count}} players, but only {{valid}} valid players were found.", "selectGroupBeforeSaving": "Please select a group before saving a match.", "matchSavedSuccess": "Match Saved Successfully!", "saveError": "Error Saving Match", "saveFailure": "Failed to save match", "noTeamsGenerated": "No teams generated yet", "generateTeamsFirst": "Generate teams first before saving.", "generatedTeams": "Generated Teams", "teamsBalanced": "Teams balanced by player ratings", "selectPlayersAndGenerate": "Select players and click Generate Teams to create balanced teams", "selectPlayersAndOptions": "Select players and generation options", "generationMode": "Generation Mode", "random": "Random", "format": "Format", "avg": "Avg", "selected": "Selected", "needed": "Needed", "selectAllPlayers": "Select All Players", "savedPreferences": "Saved Preferences"}, "chemistry": {"title": "Chemistry", "duos": "Duos", "trios": "Trios", "quads": "Quads", "player": "Player", "partner": "Partner", "winRate": "Win Rate", "winRatePercent": "Win Rate (%)", "played": "Played", "gamesPlayed": "Games Played", "searchPlayers": "Search players...", "noChemistryData": "No chemistry data available", "analysisTitle": "Chemistry Analysis", "analysisDescription": "View how player groups perform when placed on the same team.", "filterByPlayer": "Filter by player", "allPlayers": "All players", "minGames": "Min. games", "minGamesPlaceholder": "Min Games", "scatterPlotTitle": "Chemistry Scatter Plot", "scatterPlotDescription": "Visualizing win rate vs. games played for the selected group size.", "noDataForChart": "No data available for the current filters to display the chart.", "loadError": "Error loading data", "fetchError": "Error fetching chemistry data", "noDataAvailable": "No chemistry data available", "noDataMatchesFilters": "No chemistry data matches your filters", "searchPlaceholder": "Search player names ({viewMode})...", "playerFilterTitle": "Player Filter", "playerFilterDescription": "Select a player to see their chemistry with others", "selectPlayerPlaceholder": "Select a player to filter", "summary": "Chemistry Summary", "bestPartner": "Best Partner", "bestTrio": "Best Trio", "bestQuad": "Best Quad", "bestDuo": "Best Duo", "bestWith": "Best With", "showingCombinations": "Showing {{count}} {{type}}", "players": "Players", "wins": "Wins", "record": "Record", "wonFormat": "{{wins}}/{{total}}"}, "leaderboard": {"title": "Leaderboard", "rank": "Rank", "w": "W", "d": "D", "l": "L", "wc": "WC", "worldCupRunsColumn": "World Cup Runs", "minGames": "Min. games", "minGamesPlaceholder": "Min Games", "year": "Year", "yearPlaceholder": "Year", "allYears": "All Years", "noPlayersFound": "No players found matching the criteria.", "loadError": "Error loading leaderboard", "notRanked": "Not ranked", "showingPlayers": "Showing {{count}} of {{total}} players", "noData": "No data available", "sortBy": "Sort by", "filters": "Filters"}, "profile": {"title": "Profile", "personalInfo": "Personal Information", "name": "Name", "email": "Email", "settings": "Profile Settings", "settingsDescription": "Manage your account settings and preferences", "userInformation": "User Information", "security": "Security", "securityDescription": "Update your password and security settings", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "updatePassword": "Update Password", "passwordsDoNotMatch": "New passwords do not match", "passwordChangeSuccess": "Password changed successfully", "passwordChangeError": "Failed to change password", "signOutDescription": "Sign out of your account", "updateProfile": "Update Profile", "preferences": "Preferences", "preferencesDescription": "Customize your app experience", "accountInfo": "Account Information", "accountInfoDescription": "Your personal account details and profile information", "memberSince": "Member Since", "theme": "Theme", "themeDescription": "Change the appearance of the application", "language": "Language", "languageDescription": "Change the language of the application", "currentLanguage": "Current Language", "darkMode": "Dark Mode", "lightMode": "Light Mode", "english": "English", "spanish": "Spanish", "feedback": "<PERSON><PERSON><PERSON>", "feedbackDescription": "Help us improve Fulbito Stats by sharing your thoughts", "giveFeedback": "<PERSON>", "support": "Support", "supportDescription": "Help us improve and grow", "supportWithCafecito": "Support with Cafecito"}, "groups": {"title": "Groups", "selectGroup": "Select Group", "createGroup": "Create Group", "joinGroup": "Join Group", "groupName": "Group Name", "members": "Members", "role": "Role", "admin": "Admin", "collaborator": "Collaborator", "guest": "Guest", "noGroups": "No groups found", "createYourFirstGroup": "Create your first group", "welcomeToGroups": "Welcome to Fulbito Stats Groups", "groupsDescription": "Create or join groups to track statistics for different teams or leagues.", "noGroupSelected": "No group selected. Please select a group first.", "yourGroups": "Your Groups", "manageGroups": "Manage Groups", "editGroupName": "Edit Group Name", "editGroupNameDescription": "Change the name of your group.", "enterNewGroupName": "Enter new group name", "updating": "Updating...", "updateName": "Update Name", "nameUpdateSuccess": "Group name updated successfully.", "nameUpdateError": "Failed to update group name.", "adminPermissionRequired": "You need Ad<PERSON> permission to edit the group name", "loadError": "Failed to load groups.", "selectError": "Failed to select group. Please try again.", "noRole": "No Role"}, "common": {"loading": "Loading...", "save": "Save", "saveChanges": "Save Changes", "cancel": "Cancel", "delete": "Delete", "deleting": "Deleting...", "edit": "Edit", "view": "View", "search": "Search", "filter": "Filter", "sort": "Sort", "ascending": "Ascending", "descending": "Descending", "online": "Online", "offline": "Offline", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "yes": "Yes", "no": "No", "confirm": "Confirm", "actions": "Actions", "or": "Or continue with", "add": "Add", "update": "Update", "showing": "Showing", "to": "to", "of": "of", "previous": "Previous", "next": "Next", "na": "N/A", "all": "All", "openMenu": "Open menu", "navigation": "Navigation", "page": "Page", "feedback": "<PERSON><PERSON><PERSON>", "donate": "Donate", "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "jan": "Jan", "feb": "Feb", "mar": "Mar", "apr": "Apr", "jun": "Jun", "jul": "Jul", "aug": "Aug", "sep": "Sep", "oct": "Oct", "nov": "Nov", "dec": "Dec"}}, "errors": {"somethingWentWrong": "Something went wrong", "tryAgain": "Please try again", "pageNotFound": "Page not found", "returnHome": "Return to home", "unauthorized": "Unauthorized access", "loginRequired": "Login required", "invalidCredentials": "Invalid email or password", "emailAlreadyExists": "Email already exists", "passwordMismatch": "Passwords do not match", "weakPassword": "Password is too weak", "requiredField": "This field is required", "invalidEmail": "Invalid email address", "networkError": "Network error", "databaseError": "Database error", "emailNotConfirmed": "Please check your email and confirm your account before logging in.", "socialLoginFailed": "Failed to sign in with {{provider}}", "error": "Error", "validationError": "Validation Error"}, "sharing": {"title": "Sharing", "shareLink": "Share Link", "copyLink": "Copy Link", "linkCopied": "Link copied to clipboard", "viewOnly": "View Only", "commentAccess": "Comment Access", "shareWithFriends": "Share with friends", "sharingDescription": "Share your stats with friends without requiring them to create an account.", "viewing": "Viewing", "viewOnlyMode": "View-only mode.", "commentEnabledMode": "Comment-enabled mode.", "toEditData": "to edit data.", "invalidLinkId": "Invalid link ID", "featureNotAvailable": "Sharing feature is not available yet", "invalidLinkData": "Invalid link data", "failedToValidate": "Failed to validate link", "validatingLink": "Validating Shared Link", "pleaseWait": "Please wait while we validate your link...", "linkError": "<PERSON>", "linkProblem": "There was a problem with the shared link", "linkValidated": "Link Validated", "redirecting": "Redirecting to shared content...", "youNowHave": "You now have", "accessTo": "access to"}, "language": {"toggle": "Switch to {{language}}"}, "install": {"title": "Install App", "description": "Install Fulbito Stats for a better experience", "benefits": "Get offline access, faster loading, and a full-screen experience", "button": "Install Now"}, "feedback": {"title": "We'd love your feedback!", "description": "Help us improve Fulbito Stats by sharing your thoughts.", "button": "<PERSON>"}, "cafecito": {"title": "Support Fulbito Stats", "description": "If you enjoy using Fulbito Stats, consider supporting us with a cafecito.", "button": "Support with Cafecito"}, "demo": {"banner": "You are viewing the Fulbito Stats Demo", "bannerTooltip": "This is a demonstration with sample data. Sign up to create your own group and track real matches!", "mode": "Demo Mode", "backToLanding": "Back to Landing", "footerText": "This is a demonstration with sample data.", "signUpToStart": "Sign up to get started with your own group!", "readOnlyMode": "Demo Mode", "addPlayerNotAvailable": "Adding players is not available in demo mode. Sign up to manage your own players!", "editPlayerNotAvailable": "Editing players is not available in demo mode. Sign up to manage your own players!", "deletePlayerNotAvailable": "Deleting players is not available in demo mode. Sign up to manage your own players!", "addMatchNotAvailable": "Adding matches is not available in demo mode. Sign up to track your own matches!", "editMatchNotAvailable": "Editing matches is not available in demo mode. Sign up to manage your own matches!", "deleteMatchNotAvailable": "Deleting matches is not available in demo mode. Sign up to manage your own matches!", "saveMatchNotAvailable": "Saving matches is not available in demo mode. Sign up to save your generated teams as matches!", "welcomeTitle": "Welcome to the Fulbito Stats Demo!", "welcomeDescription": "Explore all features with realistic sample data. This demo includes 15 players and 30 matches to showcase the full capabilities of Fulbito Stats.", "feature1": "15 Sample Players", "feature2": "30 Match History", "feature3": "Live Statistics", "feature4": "Team Generator"}}