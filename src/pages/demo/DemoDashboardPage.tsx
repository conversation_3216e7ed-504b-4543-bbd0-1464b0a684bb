import { useState, useMemo, useEffect } from "react";
import { useTranslation } from "react-i18next";
import DemoLayout from "@/components/layout/DemoLayout";
import { useDemo } from "@/context/DemoContext";
import StatCard from "@/components/StatCard";
import {
  Users, Calendar, TrendingUp, Percent, User, ShieldCheck, Trophy, History, Sparkles, Search, LayoutDashboard, Handshake, Shuffle
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useMediaQuery } from "@/hooks/use-media-query";
import { format } from "date-fns";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { cn } from "@/lib/utils";

const DemoDashboardPage = () => {
  const { t } = useTranslation();
  const { players, matches, group } = useDemo();
  const [selectedPlayerId, setSelectedPlayerId] = useState<string>("");
  const [minGamesForLeaderboard, setMinGamesForLeaderboard] = useState<string>("3");
  const [minGamesForChemistry, setMinGamesForChemistry] = useState<string>("3");
  const [activeTab, setActiveTab] = useState("overview");
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Set page title
  useEffect(() => {
    document.title = `${t('nav.dashboard')} - ${t('app.name')} Demo`;
  }, [t]);

  // Calculate player statistics
  const playerStatsWithWinRate = useMemo(() => {
    return players.map(player => {
      const playerMatches = matches.filter(match => 
        match.teama.includes(player.id) || match.teamb.includes(player.id)
      );

      let wins = 0;
      let draws = 0;
      let losses = 0;

      playerMatches.forEach(match => {
        const isTeamA = match.teama.includes(player.id);
        
        if (match.winner === 'Draw') {
          draws++;
        } else if (
          (isTeamA && match.winner === 'A') || 
          (!isTeamA && match.winner === 'B')
        ) {
          wins++;
        } else {
          losses++;
        }
      });

      const played = playerMatches.length;
      const winRate = played > 0 ? (wins / played) * 100 : 0;

      return {
        ...player,
        played,
        wins,
        draws,
        losses,
        winRate,
      };
    });
  }, [players, matches]);

  // Get leaderboard leader
  const leaderboardLeader = useMemo(() => {
    const minGames = parseInt(minGamesForLeaderboard, 10);
    const eligiblePlayers = playerStatsWithWinRate
      .filter(p => p.played >= minGames)
      .sort((a, b) => b.winRate - a.winRate);
    
    return eligiblePlayers[0] || null;
  }, [playerStatsWithWinRate, minGamesForLeaderboard]);

  // Calculate group stats
  const groupStats = useMemo(() => {
    const totalMatches = matches.length;
    const totalPlayers = players.length;
    const totalGoals = matches.reduce((sum, match) => 
      sum + (match.scorea || 0) + (match.scoreb || 0), 0
    );
    const avgGoalsPerMatch = totalMatches > 0 ? (totalGoals / totalMatches).toFixed(1) : '0';

    return {
      totalPlayers,
      totalMatches,
      totalGoals,
      avgGoalsPerMatch,
    };
  }, [players, matches]);

  // Get recent matches (last 3)
  const recentMatches = useMemo(() => {
    return matches.slice(0, 3);
  }, [matches]);

  // Get selected player data
  const selectedPlayerData = useMemo(() => {
    if (!selectedPlayerId) return null;
    const playerId = parseInt(selectedPlayerId);
    return playerStatsWithWinRate.find(p => p.id === playerId) || null;
  }, [selectedPlayerId, playerStatsWithWinRate]);

  // Calculate chemistry data
  const chemistryData = useMemo(() => {
    const minGames = parseInt(minGamesForChemistry, 10);
    const duosMap = new Map<string, { played: number; wins: number; players: number[] }>();

    matches.forEach(match => {
      const processTeam = (team: number[], won: boolean) => {
        for (let i = 0; i < team.length; i++) {
          for (let j = i + 1; j < team.length; j++) {
            const key = [team[i], team[j]].sort().join('-');
            const existing = duosMap.get(key) || { played: 0, wins: 0, players: [team[i], team[j]].sort() };
            existing.played++;
            if (won) existing.wins++;
            duosMap.set(key, existing);
          }
        }
      };

      if (match.winner === 'A') {
        processTeam(match.teama, true);
        processTeam(match.teamb, false);
      } else if (match.winner === 'B') {
        processTeam(match.teama, false);
        processTeam(match.teamb, true);
      } else {
        // Draw - no wins for either team
        processTeam(match.teama, false);
        processTeam(match.teamb, false);
      }
    });

    const duos = Array.from(duosMap.values())
      .filter(duo => duo.played >= minGames)
      .map(duo => ({
        ...duo,
        winRate: duo.played > 0 ? (duo.wins / duo.played) * 100 : 0
      }))
      .sort((a, b) => b.winRate - a.winRate);

    return { duos: duos.slice(0, 3) }; // Top 3 duos
  }, [matches, minGamesForChemistry]);

  const getPlayerName = (playerId: number) => {
    const player = players.find(p => p.id === playerId);
    return player?.name || `Player ${playerId}`;
  };

  return (
    <DemoLayout>
      <div className="space-y-6">
        {/* Demo Info Card */}
        <Card className="border-soccer-primary/20 bg-soccer-primary/5">
          <CardContent className="pt-6">
            <div className="flex items-start gap-3">
              <div className="h-8 w-8 rounded-full bg-soccer-primary/10 flex items-center justify-center flex-shrink-0">
                <Sparkles className="h-4 w-4 text-soccer-primary" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-soccer-primary mb-2">
                  {t('demo.welcomeTitle', 'Welcome to the Fulbito Stats Demo!')}
                </h3>
                <p className="text-sm text-muted-foreground mb-3">
                  {t('demo.welcomeDescription', 'Explore all features with realistic sample data. This demo includes 15 players and 30 matches to showcase the full capabilities of Fulbito Stats.')}
                </p>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="outline" className="text-xs">
                    {t('demo.feature1', '15 Sample Players')}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {t('demo.feature2', '30 Match History')}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {t('demo.feature3', 'Live Statistics')}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {t('demo.feature4', 'Team Generator')}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tab Navigation - matching authenticated dashboard */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-4 mb-6">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger
                    value="overview"
                    className={cn(
                      "dashboard-tab",
                      activeTab === "overview" && "bg-soccer-primary/10 text-white border border-soccer-primary/20"
                    )}
                  >
                    <LayoutDashboard className="h-4 w-4 md:mr-2" />
                    <span className="hidden md:inline">{t('dashboard.overview')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  <p>{t('dashboard.overview')}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger
                    value="leaderboard"
                    className={cn(
                      "dashboard-tab",
                      activeTab === "leaderboard" && "bg-soccer-primary/10 text-white border border-soccer-primary/20"
                    )}
                  >
                    <Trophy className="h-4 w-4 md:mr-2" />
                    <span className="hidden md:inline">{t('nav.leaderboard')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  <p>{t('nav.leaderboard')}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger
                    value="chemistry"
                    className={cn(
                      "dashboard-tab",
                      activeTab === "chemistry" && "bg-soccer-primary/10 text-white border border-soccer-primary/20"
                    )}
                  >
                    <Handshake className="h-4 w-4 md:mr-2" />
                    <span className="hidden md:inline">{t('nav.chemistry')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  <p>{t('nav.chemistry')}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger
                    value="team-generator"
                    className={cn(
                      "dashboard-tab",
                      activeTab === "team-generator" && "bg-soccer-primary/10 text-white border border-soccer-primary/20"
                    )}
                  >
                    <Shuffle className="h-4 w-4 md:mr-2" />
                    <span className="hidden md:inline">{t('nav.teamGenerator')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  <p>{t('nav.teamGenerator')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </TabsList>

          <TabsContent value="overview">
            {/* Global Minimum Games Filter */}
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-6">
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium">{t('dashboard.minGamesFilter')}</label>
                <Select value={minGamesForLeaderboard} onValueChange={setMinGamesForLeaderboard}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {[0, 1, 2, 3, 4, 5].map(num => (
                      <SelectItem key={num} value={num.toString()}>{num}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

        {/* Group Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title={t('dashboard.totalPlayers')}
            value={groupStats.totalPlayers.toString()}
            icon={Users}
            trend={{ value: 0, isPositive: true }}
          />
          <StatCard
            title={t('dashboard.totalMatches')}
            value={groupStats.totalMatches.toString()}
            icon={Calendar}
            trend={{ value: 0, isPositive: true }}
          />
          <StatCard
            title={t('dashboard.totalGoals')}
            value={groupStats.totalGoals.toString()}
            icon={Trophy}
            trend={{ value: 0, isPositive: true }}
          />
          <StatCard
            title={t('dashboard.avgGoalsPerMatch')}
            value={groupStats.avgGoalsPerMatch}
            icon={TrendingUp}
            trend={{ value: 0, isPositive: true }}
          />
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Leaderboard Leader */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShieldCheck className="h-5 w-5 text-soccer-primary" />
                {t('dashboard.leaderboardLeader')}
              </CardTitle>
              <CardDescription>
                {t('dashboard.leaderboardLeaderDesc')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {leaderboardLeader ? (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="font-semibold text-lg">{leaderboardLeader.name}</span>
                    <Badge variant="secondary" className="bg-soccer-primary/10 text-soccer-primary">
                      #{1}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">{t('stats.winRate')}</span>
                      <div className="font-semibold">{leaderboardLeader.winRate.toFixed(1)}%</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">{t('stats.played')}</span>
                      <div className="font-semibold">{leaderboardLeader.played}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">{t('stats.wins')}</span>
                      <div className="font-semibold text-green-600">{leaderboardLeader.wins}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">{t('stats.losses')}</span>
                      <div className="font-semibold text-red-600">{leaderboardLeader.losses}</div>
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-4">
                  {t('dashboard.noLeaderData')}
                </p>
              )}
            </CardContent>
          </Card>

          {/* Chemistry Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-soccer-primary" />
                {t('dashboard.bestChemistry')}
              </CardTitle>
              <CardDescription>
                {t('dashboard.bestChemistryDesc')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {chemistryData.duos.length > 0 ? (
                <div className="space-y-3">
                  {chemistryData.duos.map((duo, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="font-medium text-sm">
                          {getPlayerName(duo.players[0])} & {getPlayerName(duo.players[1])}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {t('chemistry.wonFormat', { wins: duo.wins, total: duo.played })}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-sm">{duo.winRate.toFixed(1)}%</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-4">
                  {t('dashboard.noChemistryData')}
                </p>
              )}
            </CardContent>
          </Card>

          {/* Player Snapshot */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5 text-soccer-primary" />
                {t('dashboard.playerSnapshot')}
              </CardTitle>
              <CardDescription>
                {t('dashboard.playerSnapshotDesc')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Select value={selectedPlayerId} onValueChange={setSelectedPlayerId}>
                  <SelectTrigger>
                    <SelectValue placeholder={t('dashboard.selectPlayer')} />
                  </SelectTrigger>
                  <SelectContent>
                    {playerStatsWithWinRate
                      .sort((a, b) => b.played - a.played)
                      .map(player => (
                        <SelectItem key={player.id} value={player.id.toString()}>
                          {player.name} ({player.played} {t('stats.games')})
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>

                {selectedPlayerData && (
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">{t('stats.winRate')}</span>
                      <div className="font-semibold">{selectedPlayerData.winRate.toFixed(1)}%</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">{t('stats.played')}</span>
                      <div className="font-semibold">{selectedPlayerData.played}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">{t('stats.wins')}</span>
                      <div className="font-semibold text-green-600">{selectedPlayerData.wins}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">{t('stats.losses')}</span>
                      <div className="font-semibold text-red-600">{selectedPlayerData.losses}</div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Matches */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <History className="h-5 w-5 text-soccer-primary" />
              {t('dashboard.recentMatches')}
            </CardTitle>
            <CardDescription>
              {t('dashboard.recentMatchesDesc')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentMatches.length > 0 ? (
              <div className="space-y-3">
                {recentMatches.map((match) => (
                  <div key={match.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium text-sm">
                        {format(new Date(match.match_date), 'MMM dd, yyyy')}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {match.teama.length}v{match.teamb.length}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="font-bold text-lg">
                        {match.scorea} - {match.scoreb}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {match.winner === 'Draw' ? t('matches.draw') : 
                         match.winner === 'A' ? t('matches.teamAWin') : t('matches.teamBWin')}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground text-center py-4">
                {t('dashboard.noRecentMatches')}
              </p>
            )}
          </CardContent>
        </Card>
          </TabsContent>

          <TabsContent value="leaderboard">
            <div className="text-center py-8">
              <Trophy className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
              <p className="text-muted-foreground">{t('demo.leaderboardTab', 'Visit the Leaderboard page to see detailed rankings and statistics.')}</p>
            </div>
          </TabsContent>

          <TabsContent value="chemistry">
            <div className="text-center py-8">
              <Handshake className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
              <p className="text-muted-foreground">{t('demo.chemistryTab', 'Visit the Chemistry page to see player combinations and partnerships.')}</p>
            </div>
          </TabsContent>

          <TabsContent value="team-generator">
            <div className="text-center py-8">
              <Shuffle className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
              <p className="text-muted-foreground">{t('demo.teamGeneratorTab', 'Visit the Team Generator page to create balanced teams.')}</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DemoLayout>
  );
};

export default DemoDashboardPage;
