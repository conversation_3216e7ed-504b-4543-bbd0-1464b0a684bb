import { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import DemoLayout from "@/components/layout/DemoLayout";
import { useDemo } from "@/context/DemoContext";
import { useMediaQuery } from "@/hooks/use-media-query";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Trophy, Medal, Award, Filter } from "lucide-react";
import { MobileTable } from "@/components/ui/mobile-table";

// Enhanced components
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { ResponsiveTable } from "@/components/ui/responsive-table";

interface LeaderboardPlayer {
  id: number;
  name: string;
  skills: number;
  effort: number;
  stamina: number;
  played: number;
  wins: number;
  draws: number;
  losses: number;
  winRate: number;
  winStreak: number;
  worldCupRuns: number;
}

const DemoLeaderboardPage = () => {
  const { t } = useTranslation();
  const { players, matches } = useDemo();
  const [minGames, setMinGames] = useState("3");
  const [sortField, setSortField] = useState<keyof LeaderboardPlayer>("winRate");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [yearFilter, setYearFilter] = useState("all");
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Set page title
  useEffect(() => {
    document.title = `${t('nav.leaderboard')} - ${t('app.name')} Demo`;
  }, [t]);

  // Calculate leaderboard data
  const leaderboardData = useMemo(() => {
    // Filter matches by year if needed
    const filteredMatches = yearFilter === 'all' 
      ? matches 
      : matches.filter(match => {
          const matchDate = new Date(match.match_date);
          return matchDate.getFullYear() === parseInt(yearFilter);
        });

    const playerStats: LeaderboardPlayer[] = players.map(player => {
      const playerMatches = filteredMatches.filter(match => 
        match.teama.includes(player.id) || match.teamb.includes(player.id)
      );

      let wins = 0;
      let draws = 0;
      let losses = 0;
      let currentStreak = 0;
      let maxStreak = 0;
      let worldCupRuns = 0;

      // Sort matches by date for streak calculation
      const sortedMatches = playerMatches.sort((a, b) => 
        new Date(b.match_date).getTime() - new Date(a.match_date).getTime()
      );

      sortedMatches.forEach((match, index) => {
        const isTeamA = match.teama.includes(player.id);
        let won = false;

        if (match.winner === 'Draw') {
          draws++;
          currentStreak = 0;
        } else if (
          (isTeamA && match.winner === 'A') || 
          (!isTeamA && match.winner === 'B')
        ) {
          wins++;
          won = true;
          currentStreak++;
          maxStreak = Math.max(maxStreak, currentStreak);
        } else {
          losses++;
          currentStreak = 0;
        }
      });

      // Calculate World Cup Runs (simplified - count completed runs of 7 matches)
      worldCupRuns = Math.floor(playerMatches.length / 7);

      const played = playerMatches.length;
      const winRate = played > 0 ? (wins / played) * 100 : 0;

      return {
        id: player.id,
        name: player.name,
        skills: player.skills,
        effort: player.effort,
        stamina: player.stamina,
        played,
        wins,
        draws,
        losses,
        winRate,
        winStreak: maxStreak,
        worldCupRuns,
      };
    });

    // Filter by minimum games
    const minGamesNum = parseInt(minGames);
    const filteredStats = playerStats.filter(player => player.played >= minGamesNum);

    // Sort by selected field
    filteredStats.sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'desc' ? bValue - aValue : aValue - bValue;
      } else {
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        return sortDirection === 'desc' ? bStr.localeCompare(aStr) : aStr.localeCompare(bStr);
      }
    });

    return filteredStats;
  }, [players, matches, minGames, sortField, sortDirection, yearFilter]);

  // Get available years from matches
  const availableYears = useMemo(() => {
    const years = new Set(matches.map(match => new Date(match.match_date).getFullYear()));
    return Array.from(years).sort((a, b) => b - a);
  }, [matches]);

  // Get rank icon
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="h-5 w-5 text-yellow-500" />;
      case 2:
        return <Medal className="h-5 w-5 text-gray-400" />;
      case 3:
        return <Award className="h-5 w-5 text-amber-600" />;
      default:
        return (
          <div className="h-6 w-6 rounded-full border-2 border-soccer-primary bg-soccer-primary/10 flex items-center justify-center">
            <span className="text-xs font-bold text-soccer-primary">{rank}</span>
          </div>
        );
    }
  };

  // Handle sort
  const handleSort = (field: keyof LeaderboardPlayer) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'desc' ? 'asc' : 'desc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Desktop table columns
  const columns = [
    {
      header: t('leaderboard.rank'),
      accessorKey: 'rank',
      cell: (player: LeaderboardPlayer, index: number) => (
        <div className="flex items-center gap-2">
          {getRankIcon(index + 1)}
        </div>
      ),
    },
    {
      header: t('leaderboard.player'),
      accessorKey: 'name',
      cell: (player: LeaderboardPlayer) => (
        <span className="font-medium">{player.name}</span>
      ),
    },
    {
      header: t('stats.played'),
      accessorKey: 'played',
      sortable: true,
      cell: (player: LeaderboardPlayer) => (
        <span className="font-medium">{player.played}</span>
      ),
    },
    {
      header: t('stats.winRate'),
      accessorKey: 'winRate',
      sortable: true,
      cell: (player: LeaderboardPlayer) => (
        <span className="font-medium">{player.winRate.toFixed(1)}%</span>
      ),
    },
    {
      header: t('stats.wins'),
      accessorKey: 'wins',
      sortable: true,
      cell: (player: LeaderboardPlayer) => (
        <span className="font-medium text-green-600">{player.wins}</span>
      ),
    },
    {
      header: t('stats.draws'),
      accessorKey: 'draws',
      sortable: true,
      cell: (player: LeaderboardPlayer) => (
        <span className="font-medium text-yellow-600">{player.draws}</span>
      ),
    },
    {
      header: t('stats.losses'),
      accessorKey: 'losses',
      sortable: true,
      cell: (player: LeaderboardPlayer) => (
        <span className="font-medium text-red-600">{player.losses}</span>
      ),
    },
    {
      header: t('stats.winStreak'),
      accessorKey: 'winStreak',
      sortable: true,
      cell: (player: LeaderboardPlayer) => (
        <Badge variant="outline">{player.winStreak}</Badge>
      ),
    },
    {
      header: t('stats.worldCupRuns'),
      accessorKey: 'worldCupRuns',
      sortable: true,
      cell: (player: LeaderboardPlayer) => (
        <Badge variant="secondary">{player.worldCupRuns}</Badge>
      ),
    },
  ];

  return (
    <DemoLayout>
      <div className="space-y-6">
        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              {t('leaderboard.filters')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">{t('leaderboard.minGames')}</label>
                <Select value={minGames} onValueChange={setMinGames}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {[0, 1, 2, 3, 4, 5, 10].map(num => (
                      <SelectItem key={num} value={num.toString()}>
                        {num} {t('stats.games')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">{t('leaderboard.year')}</label>
                <Select value={yearFilter} onValueChange={setYearFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('leaderboard.allYears')}</SelectItem>
                    {availableYears.map(year => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">{t('leaderboard.sortBy')}</label>
                <Select value={sortField} onValueChange={(value) => setSortField(value as keyof LeaderboardPlayer)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="winRate">{t('stats.winRate')}</SelectItem>
                    <SelectItem value="played">{t('stats.played')}</SelectItem>
                    <SelectItem value="wins">{t('stats.wins')}</SelectItem>
                    <SelectItem value="winStreak">{t('stats.winStreak')}</SelectItem>
                    <SelectItem value="worldCupRuns">{t('stats.worldCupRuns')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Leaderboard Table */}
        {leaderboardData.length === 0 ? (
          <Card>
            <CardContent className="py-8">
              <div className="text-center text-muted-foreground">
                {t('leaderboard.noData')}
              </div>
            </CardContent>
          </Card>
        ) : (
          <>
            {isMobile ? (
              <MobileTable
                data={leaderboardData}
                columns={[
                  {
                    header: t('leaderboard.rank'),
                    accessorKey: 'rank' as keyof LeaderboardPlayer,
                    priority: "high",
                    cell: (player: LeaderboardPlayer, index: number) => getRankIcon(index + 1),
                  },
                  {
                    header: t('leaderboard.player'),
                    accessorKey: 'name' as keyof LeaderboardPlayer,
                    priority: "high",
                    cell: (player: LeaderboardPlayer) => (
                      <span className="font-medium">{player.name}</span>
                    ),
                  },
                  {
                    header: t('stats.winRate'),
                    accessorKey: 'winRate' as keyof LeaderboardPlayer,
                    priority: "high",
                    cell: (player: LeaderboardPlayer) => (
                      <span className="font-medium">{player.winRate.toFixed(1)}%</span>
                    ),
                  },
                  {
                    header: t('stats.played'),
                    accessorKey: 'played' as keyof LeaderboardPlayer,
                    priority: "medium",
                    cell: (player: LeaderboardPlayer) => (
                      <span className="font-medium">{player.played}</span>
                    ),
                  },
                ]}
                emptyMessage={t('leaderboard.noData')}
              />
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      {columns.map((column, index) => (
                        <TableHead 
                          key={index}
                          className={column.sortable ? "cursor-pointer hover:bg-muted/50" : ""}
                          onClick={column.sortable ? () => handleSort(column.accessorKey as keyof LeaderboardPlayer) : undefined}
                        >
                          <div className="flex items-center gap-1">
                            {column.header}
                            {column.sortable && sortField === column.accessorKey && (
                              <span className="text-xs">
                                {sortDirection === 'desc' ? '↓' : '↑'}
                              </span>
                            )}
                          </div>
                        </TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {leaderboardData.map((player, index) => (
                      <TableRow key={player.id}>
                        {columns.map((column, colIndex) => (
                          <TableCell key={colIndex}>
                            {column.cell(player, index)}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </>
        )}

        {/* Player count */}
        <div className="text-sm text-muted-foreground text-center">
          {t('leaderboard.showingPlayers', { 
            count: leaderboardData.length, 
            total: players.length 
          })}
        </div>
      </div>
    </DemoLayout>
  );
};

export default DemoLeaderboardPage;
