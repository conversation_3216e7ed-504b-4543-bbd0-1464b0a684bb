import { useState, useEffect, use<PERSON>emo } from "react";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@/components/ui/button";
import DemoLayout from "@/components/layout/DemoLayout";
import { useDemo } from "@/context/DemoContext";
import { EmptyState } from "@/components/ui/empty-state";
import { MobileTable } from "@/components/ui/mobile-table";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useToast } from "@/hooks/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Plus, Calendar, Filter } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";

// Enhanced components
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { ResponsiveTable } from "@/components/ui/responsive-table";
import { AnimatedButton } from "@/components/ui/animated-button";
import { EnhancedInput } from "@/components/ui/enhanced-input";

const DemoMatchesPage = () => {
  const { t } = useTranslation();
  const { players, matches } = useDemo();
  const [loading, setLoading] = useState(false);
  const isMobile = useMediaQuery("(max-width: 768px)");
  const { toast } = useToast();

  // Filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [resultFilter, setResultFilter] = useState("all");
  const [sortBy, setSortBy] = useState("date-desc");

  // Set page title
  useEffect(() => {
    document.title = `${t('nav.matches')} - ${t('app.name')} Demo`;
  }, [t]);

  // Get player name helper
  const getPlayerName = (playerId: number) => {
    const player = players.find(p => p.id === playerId);
    return player?.name || `Player ${playerId}`;
  };

  // Filter and sort matches
  const filteredAndSortedMatches = useMemo(() => {
    let filtered = [...matches];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(match => {
        const allPlayerNames = [...match.teama, ...match.teamb]
          .map(id => getPlayerName(id))
          .join(' ')
          .toLowerCase();
        return allPlayerNames.includes(searchTerm.toLowerCase());
      });
    }

    // Apply result filter
    if (resultFilter !== "all") {
      filtered = filtered.filter(match => {
        if (resultFilter === "wins") return match.winner !== 'Draw';
        if (resultFilter === "draws") return match.winner === 'Draw';
        return true;
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const dateA = new Date(a.match_date).getTime();
      const dateB = new Date(b.match_date).getTime();
      
      switch (sortBy) {
        case "date-asc":
          return dateA - dateB;
        case "date-desc":
        default:
          return dateB - dateA;
      }
    });

    return filtered;
  }, [matches, searchTerm, resultFilter, sortBy, getPlayerName]);

  const handleAddMatch = () => {
    toast({
      title: t('demo.readOnlyMode'),
      description: t('demo.addMatchNotAvailable', 'Adding matches is not available in demo mode. Sign up to track your own matches!'),
      variant: 'default',
    });
  };

  const handleEditMatch = () => {
    toast({
      title: t('demo.readOnlyMode'),
      description: t('demo.editMatchNotAvailable', 'Editing matches is not available in demo mode. Sign up to manage your own matches!'),
      variant: 'default',
    });
  };

  const handleDeleteMatch = () => {
    toast({
      title: t('demo.readOnlyMode'),
      description: t('demo.deleteMatchNotAvailable', 'Deleting matches is not available in demo mode. Sign up to manage your own matches!'),
      variant: 'default',
    });
  };

  // Get result badge
  const getResultBadge = (match: any) => {
    if (match.winner === 'Draw') {
      return <Badge variant="secondary">{t('matches.draw')}</Badge>;
    } else if (match.winner === 'A') {
      return <Badge variant="default" className="bg-green-600 hover:bg-green-700">{t('matches.teamAWin')}</Badge>;
    } else {
      return <Badge variant="default" className="bg-green-600 hover:bg-green-700">{t('matches.teamBWin')}</Badge>;
    }
  };

  // Desktop table columns
  const columns = [
    {
      header: t('matches.date'),
      accessorKey: 'match_date',
      cell: (match: any) => (
        <div className="font-medium">
          {format(new Date(match.match_date), 'MMM dd, yyyy')}
        </div>
      ),
    },
    {
      header: t('matches.teams'),
      accessorKey: 'teams',
      cell: (match: any) => (
        <div className="space-y-1">
          <div className="text-sm">
            <span className="font-medium">Team A:</span> {match.teama.map(id => getPlayerName(id)).join(', ')}
          </div>
          <div className="text-sm">
            <span className="font-medium">Team B:</span> {match.teamb.map(id => getPlayerName(id)).join(', ')}
          </div>
        </div>
      ),
    },
    {
      header: t('matches.score'),
      accessorKey: 'score',
      cell: (match: any) => (
        <div className="text-center">
          <div className="font-bold text-lg">
            {match.scorea} - {match.scoreb}
          </div>
        </div>
      ),
    },
    {
      header: t('matches.result'),
      accessorKey: 'result',
      cell: (match: any) => getResultBadge(match),
    },
    {
      header: t('matches.format'),
      accessorKey: 'format',
      cell: (match: any) => (
        <Badge variant="outline">
          {match.teama.length}v{match.teamb.length}
        </Badge>
      ),
    },
  ];

  return (
    <DemoLayout>
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">{t('matches.title')}</h1>
          <div>
            <Button
              onClick={handleAddMatch}
              className="bg-soccer-primary hover:bg-soccer-primary/90"
              disabled={loading}
            >
              <Plus className="mr-2 h-4 w-4" /> {t('matches.addMatch')}
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              {t('matches.filters')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">{t('matches.searchPlayers')}</label>
                <Input
                  placeholder={t('matches.searchPlayersPlaceholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">{t('matches.filterByResult')}</label>
                <Select value={resultFilter} onValueChange={setResultFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('matches.allResults')}</SelectItem>
                    <SelectItem value="wins">{t('matches.winsOnly')}</SelectItem>
                    <SelectItem value="draws">{t('matches.drawsOnly')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">{t('matches.sortBy')}</label>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date-desc">{t('matches.newestFirst')}</SelectItem>
                    <SelectItem value="date-asc">{t('matches.oldestFirst')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {filteredAndSortedMatches.length === 0 ? (
          <EmptyState
            icon={Calendar}
            title={t('matches.noMatches')}
            description={searchTerm || resultFilter !== "all" ? 
              t('matches.noMatchesFiltered') : 
              t('matches.noMatchesDesc')
            }
            action={
              <Button onClick={handleAddMatch} className="bg-soccer-primary hover:bg-soccer-primary/90">
                <Plus className="mr-2 h-4 w-4" />
                {t('matches.addFirstMatch')}
              </Button>
            }
          />
        ) : (
          <>
            {isMobile ? (
              <MobileTable
                data={filteredAndSortedMatches}
                columns={[
                  {
                    header: t('matches.date'),
                    accessorKey: 'match_date' as keyof typeof filteredAndSortedMatches[0],
                    priority: "high",
                    cell: (match: any) => (
                      <div className="font-medium">
                        {format(new Date(match.match_date), 'MMM dd')}
                      </div>
                    ),
                  },
                  {
                    header: t('matches.score'),
                    accessorKey: 'score' as keyof typeof filteredAndSortedMatches[0],
                    priority: "high",
                    cell: (match: any) => (
                      <div className="font-bold">
                        {match.scorea} - {match.scoreb}
                      </div>
                    ),
                  },
                  {
                    header: t('matches.result'),
                    accessorKey: 'result' as keyof typeof filteredAndSortedMatches[0],
                    priority: "medium",
                    cell: (match: any) => getResultBadge(match),
                  },
                  {
                    header: t('matches.format'),
                    accessorKey: 'format' as keyof typeof filteredAndSortedMatches[0],
                    priority: "low",
                    cell: (match: any) => (
                      <Badge variant="outline">
                        {match.teama.length}v{match.teamb.length}
                      </Badge>
                    ),
                  },
                ]}
                onRowClick={handleEditMatch}
                emptyMessage={t('matches.noMatches')}
              />
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      {columns.map((column, index) => (
                        <TableHead key={index}>{column.header}</TableHead>
                      ))}
                      <TableHead className="w-[100px]">{t('common.actions')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAndSortedMatches.map((match) => (
                      <TableRow key={match.id} className="cursor-pointer hover:bg-muted/50">
                        {columns.map((column, index) => (
                          <TableCell key={index}>
                            {column.cell(match)}
                          </TableCell>
                        ))}
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditMatch();
                              }}
                            >
                              {t('common.edit')}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteMatch();
                              }}
                              className="text-red-600 hover:text-red-700"
                            >
                              {t('common.delete')}
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </>
        )}

        {/* Match count */}
        <div className="text-sm text-muted-foreground text-center">
          {t('matches.showingMatches', { 
            count: filteredAndSortedMatches.length, 
            total: matches.length 
          })}
        </div>
      </div>
    </DemoLayout>
  );
};

export default DemoMatchesPage;
