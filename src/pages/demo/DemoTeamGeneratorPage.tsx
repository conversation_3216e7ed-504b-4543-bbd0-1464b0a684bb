import { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import DemoLayout from "@/components/layout/DemoLayout";
import { useDemo } from "@/context/DemoContext";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Shuffle, Users, AlertTriangle, Play } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";

type GameFormatKey = "5v5" | "6v6" | "7v7" | "8v8" | "11v11";
type GenerationMode = "balanced" | "random" | "skills" | "effort";

interface GameFormat {
  title: string;
  playersPerTeam: number;
}

const gameFormats: Record<GameFormatKey, GameFormat> = {
  "5v5": { title: "5v5", playersPerTeam: 5 },
  "6v6": { title: "6v6", playersPerTeam: 6 },
  "7v7": { title: "7v7", playersPerTeam: 7 },
  "8v8": { title: "8v8", playersPerTeam: 8 },
  "11v11": { title: "11v11", playersPerTeam: 11 },
};

const DemoTeamGeneratorPage = () => {
  const { t } = useTranslation();
  const { players } = useDemo();
  const { toast } = useToast();
  
  const [selectedPlayers, setSelectedPlayers] = useState<number[]>([]);
  const [gameFormat, setGameFormat] = useState<GameFormatKey>("5v5");
  const [generationMode, setGenerationMode] = useState<GenerationMode>("balanced");
  const [teamA, setTeamA] = useState<number[]>([]);
  const [teamB, setTeamB] = useState<number[]>([]);
  const [hasGeneratedTeams, setHasGeneratedTeams] = useState(false);

  // Set page title
  useEffect(() => {
    document.title = `${t('nav.teamGenerator')} - ${t('app.name')} Demo`;
  }, [t]);

  // Calculate player ratings
  const playersWithRatings = useMemo(() => {
    return players.map(player => ({
      ...player,
      avgRating: Math.round((player.skills + player.effort + player.stamina) / 3),
    }));
  }, [players]);

  // Get player name helper
  const getPlayerName = (playerId: number) => {
    const player = players.find(p => p.id === playerId);
    return player?.name || `Player ${playerId}`;
  };

  // Calculate team strength
  const calculateTeamStrength = (team: number[]) => {
    return team.reduce((total, playerId) => {
      const player = playersWithRatings.find(p => p.id === playerId);
      if (!player) return total;
      
      switch (generationMode) {
        case "skills":
          return total + player.skills;
        case "effort":
          return total + player.effort;
        default:
          return total + player.avgRating;
      }
    }, 0) / team.length;
  };

  // Generate teams
  const generateTeams = () => {
    const playersPerTeam = gameFormats[gameFormat].playersPerTeam;
    const requiredPlayers = playersPerTeam * 2;

    if (selectedPlayers.length < requiredPlayers) {
      toast({
        title: t('teamGenerator.notEnoughPlayers'),
        description: t('teamGenerator.needMorePlayers', { needed: requiredPlayers, selected: selectedPlayers.length }),
        variant: 'destructive',
      });
      return;
    }

    const availablePlayers = selectedPlayers.slice(0, requiredPlayers);

    if (generationMode === "random") {
      // Random generation
      const shuffled = [...availablePlayers].sort(() => Math.random() - 0.5);
      setTeamA(shuffled.slice(0, playersPerTeam));
      setTeamB(shuffled.slice(playersPerTeam));
    } else {
      // Balanced generation
      const playerData = availablePlayers.map(id => {
        const player = playersWithRatings.find(p => p.id === id)!;
        return {
          id,
          rating: generationMode === "skills" ? player.skills :
                  generationMode === "effort" ? player.effort :
                  player.avgRating
        };
      }).sort((a, b) => b.rating - a.rating);

      const newTeamA: number[] = [];
      const newTeamB: number[] = [];

      // Distribute players alternately to balance teams
      playerData.forEach((player, index) => {
        if (index % 2 === 0) {
          if (newTeamA.length < playersPerTeam) {
            newTeamA.push(player.id);
          } else {
            newTeamB.push(player.id);
          }
        } else {
          if (newTeamB.length < playersPerTeam) {
            newTeamB.push(player.id);
          } else {
            newTeamA.push(player.id);
          }
        }
      });

      setTeamA(newTeamA);
      setTeamB(newTeamB);
    }

    setHasGeneratedTeams(true);
  };

  // Handle player selection
  const togglePlayerSelection = (playerId: number) => {
    setSelectedPlayers(prev => 
      prev.includes(playerId) 
        ? prev.filter(id => id !== playerId)
        : [...prev, playerId]
    );
  };

  // Select all players
  const selectAllPlayers = () => {
    setSelectedPlayers(players.map(p => p.id));
  };

  // Clear selection
  const clearSelection = () => {
    setSelectedPlayers([]);
  };

  // Handle save match (demo mode)
  const handleSaveMatch = () => {
    toast({
      title: t('demo.readOnlyMode'),
      description: t('demo.saveMatchNotAvailable', 'Saving matches is not available in demo mode. Sign up to save your generated teams as matches!'),
      variant: 'default',
    });
  };

  const playersPerTeam = gameFormats[gameFormat].playersPerTeam;
  const requiredPlayers = playersPerTeam * 2;
  const teamAStrength = hasGeneratedTeams ? calculateTeamStrength(teamA) : 0;
  const teamBStrength = hasGeneratedTeams ? calculateTeamStrength(teamB) : 0;
  const strengthDifference = Math.abs(teamAStrength - teamBStrength);
  const isUnbalanced = strengthDifference > 1.0;

  return (
    <DemoLayout>
      <div className="space-y-6">
        {/* Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shuffle className="h-5 w-5" />
              {t('teamGenerator.configuration')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">{t('teamGenerator.gameFormat')}</label>
                <Select value={gameFormat} onValueChange={(value) => setGameFormat(value as GameFormatKey)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(gameFormats).map(([key, format]) => (
                      <SelectItem key={key} value={key}>
                        {format.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">{t('teamGenerator.generationMode')}</label>
                <Select value={generationMode} onValueChange={(value) => setGenerationMode(value as GenerationMode)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="balanced">{t('teamGenerator.balanced')}</SelectItem>
                    <SelectItem value="random">{t('teamGenerator.random')}</SelectItem>
                    <SelectItem value="skills">{t('teamGenerator.bySkills')}</SelectItem>
                    <SelectItem value="effort">{t('teamGenerator.byEffort')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-end">
                <Button 
                  onClick={generateTeams}
                  className="w-full bg-soccer-primary hover:bg-soccer-primary/90"
                  disabled={selectedPlayers.length < requiredPlayers}
                >
                  <Shuffle className="mr-2 h-4 w-4" />
                  {t('teamGenerator.generateTeams')}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Available Players */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              {t('teamGenerator.availablePlayers')} ({selectedPlayers.length}/{requiredPlayers})
            </CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={selectAllPlayers}>
                {t('teamGenerator.selectAll')}
              </Button>
              <Button variant="outline" size="sm" onClick={clearSelection}>
                {t('teamGenerator.clearSelection')}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 max-h-60 overflow-y-auto">
              {playersWithRatings.map((player) => (
                <div
                  key={player.id}
                  className="flex items-center space-x-2 p-2 border rounded-lg hover:bg-muted/50 cursor-pointer"
                  onClick={() => togglePlayerSelection(player.id)}
                >
                  <Checkbox
                    checked={selectedPlayers.includes(player.id)}
                    onChange={() => togglePlayerSelection(player.id)}
                  />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm truncate">{player.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {t('players.avgRating')}: {player.avgRating}
                    </div>
                  </div>
                </div>
              ))}
            </div>
            {selectedPlayers.length < requiredPlayers && (
              <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <div className="flex items-center gap-2 text-yellow-800 dark:text-yellow-200">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="text-sm">
                    {t('teamGenerator.needMorePlayers', { 
                      needed: requiredPlayers, 
                      selected: selectedPlayers.length 
                    })}
                  </span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Generated Teams */}
        {hasGeneratedTeams && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Play className="h-5 w-5" />
                {t('teamGenerator.generatedTeams')}
              </CardTitle>
              {isUnbalanced && (
                <div className="flex items-center gap-2 text-yellow-600 dark:text-yellow-400">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="text-sm">{t('teamGenerator.unbalancedWarning')}</span>
                </div>
              )}
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Team A */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-lg">{t('teamGenerator.teamA')}</h3>
                    <Badge variant="outline">
                      {t('teamGenerator.avgRating')}: {teamAStrength.toFixed(1)}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    {teamA.map((playerId) => (
                      <div key={playerId} className="flex items-center justify-between p-2 border rounded">
                        <span className="font-medium">{getPlayerName(playerId)}</span>
                        <Badge variant="secondary">
                          {playersWithRatings.find(p => p.id === playerId)?.avgRating}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Team B */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-lg">{t('teamGenerator.teamB')}</h3>
                    <Badge variant="outline">
                      {t('teamGenerator.avgRating')}: {teamBStrength.toFixed(1)}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    {teamB.map((playerId) => (
                      <div key={playerId} className="flex items-center justify-between p-2 border rounded">
                        <span className="font-medium">{getPlayerName(playerId)}</span>
                        <Badge variant="secondary">
                          {playersWithRatings.find(p => p.id === playerId)?.avgRating}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-center">
                <Button 
                  onClick={handleSaveMatch}
                  className="bg-soccer-primary hover:bg-soccer-primary/90"
                >
                  <Play className="mr-2 h-4 w-4" />
                  {t('teamGenerator.saveAsMatch')}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DemoLayout>
  );
};

export default DemoTeamGeneratorPage;
